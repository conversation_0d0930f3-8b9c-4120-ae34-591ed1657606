import ebooklib
from ebooklib import epub
from bs4 import BeautifulSoup
import openai
import json
import logging
import time
import re
from typing import List, Dict, Optional
import os
from datetime import datetime
import signal
import sys
import pickle
from pathlib import Path
import traceback
from functools import wraps

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('novel_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间
        backoff: 延迟倍数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            current_delay = delay
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries:
                        logger.error(f"函数 {func.__name__} 在 {max_retries} 次重试后仍然失败: {e}")
                        raise
                    
                    logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}")
                    logger.info(f"等待 {current_delay:.1f} 秒后重试...")
                    time.sleep(current_delay)
                    current_delay *= backoff
                    
            return None
        return wrapper
    return decorator

class InterruptHandler:
    """中断处理器"""
    def __init__(self):
        self.interrupted = False
        signal.signal(signal.SIGINT, self._handle_interrupt)
        signal.signal(signal.SIGTERM, self._handle_interrupt)
    
    def _handle_interrupt(self, signum, frame):
        logger.info("收到中断信号，正在安全退出...")
        self.interrupted = True
    
    def should_continue(self) -> bool:
        return not self.interrupted

class ProgressManager:
    """进度管理器"""
    def __init__(self, progress_file: str = "analysis_progress.pkl"):
        self.progress_file = progress_file
        self.progress = {
            'completed_chapters': [],
            'failed_chapters': [],
            'current_chapter_index': 0,
            'total_chapters': 0,
            'start_time': None,
            'last_save_time': None
        }
        self.load_progress()
    
    def load_progress(self):
        """加载进度"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'rb') as f:
                    self.progress = pickle.load(f)
                logger.info(f"已加载进度: {len(self.progress['completed_chapters'])}/{self.progress['total_chapters']} 章节完成")
            except Exception as e:
                logger.error(f"加载进度文件失败: {e}")
    
    def save_progress(self):
        """保存进度"""
        try:
            self.progress['last_save_time'] = datetime.now().isoformat()
            with open(self.progress_file, 'wb') as f:
                pickle.dump(self.progress, f)
            logger.debug("进度已保存")
        except Exception as e:
            logger.error(f"保存进度失败: {e}")
    
    def is_chapter_completed(self, chapter_index: int) -> bool:
        """检查章节是否已完成"""
        return chapter_index in self.progress['completed_chapters']
    
    def mark_chapter_completed(self, chapter_index: int):
        """标记章节完成"""
        if chapter_index not in self.progress['completed_chapters']:
            self.progress['completed_chapters'].append(chapter_index)
            self.progress['current_chapter_index'] = chapter_index + 1
            self.save_progress()
    
    def mark_chapter_failed(self, chapter_index: int, error: str):
        """标记章节失败"""
        self.progress['failed_chapters'].append({
            'index': chapter_index,
            'error': str(error),
            'timestamp': datetime.now().isoformat()
        })
        self.save_progress()
    
    def get_progress_info(self) -> Dict:
        """获取进度信息"""
        completed = len(self.progress['completed_chapters'])
        failed = len(self.progress['failed_chapters'])
        total = self.progress['total_chapters']
        
        return {
            'completed': completed,
            'failed': failed,
            'remaining': total - completed - failed,
            'total': total,
            'completion_rate': completed / total * 100 if total > 0 else 0,
            'current_index': self.progress['current_chapter_index']
        }
    
    def reset_progress(self):
        """重置进度"""
        if os.path.exists(self.progress_file):
            os.remove(self.progress_file)
        self.progress = {
            'completed_chapters': [],
            'failed_chapters': [],
            'current_chapter_index': 0,
            'total_chapters': 0,
            'start_time': None,
            'last_save_time': None
        }
        logger.info("进度已重置")

class EPUBAnalyzer:
    def __init__(self, api_key: str, base_url: str = "https://api.openai.com/v1"):
        """
        初始化EPUB分析器
        
        Args:
            api_key: OpenAI API密钥
            base_url: OpenAI兼容接口的基础URL
        """
        self.client = openai.OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        
        # 初始化中断处理和进度管理
        self.interrupt_handler = InterruptHandler()
        self.progress_manager = ProgressManager()
        
        # API配置
        self.max_retries = 5  # 增加重试次数
        self.retry_delay = 3.0  # 增加重试延迟
        self.rate_limit_delay = 2.0  # 增加API调用间隔
        self.api_timeout = 180.0  # 增加超时时间到3分钟
        self.max_tokens = 3000  # 增加最大token数确保完整输出
        self.model = "gpt-3.5-turbo"  # 默认模型
        
        # 金句分析提示词
        self.prompt = """
你是一个专业的文学分析师，请分析以下小说章节内容，找出其中的金句。

分析要求：
1. 识别文中的金句（具有哲理性、启发性、情感共鸣或文学价值的句子）
2. 确定每个金句是谁说的（角色名称，如果是叙述则标注为"叙述者"）
3. 不要修改原文内容，保持金句的原始表达
4. 每个金句需要简要说明为什么被选为金句

请按以下JSON格式返回结果：
{
    "chapter_title": "章节标题",
    "golden_sentences": [
        {
            "sentence": "金句原文",
            "speaker": "说话人",
            "reason": "选择理由"
        }
    ],
    "chapter_summary": "章节简要总结"
}

章节内容：
"""

    def extract_chapters(self, epub_path: str) -> List[Dict]:
        """
        从EPUB文件中提取章节内容
        
        Args:
            epub_path: EPUB文件路径
            
        Returns:
            包含章节信息的列表
        """
        logger.info(f"开始提取EPUB文件: {epub_path}")
        
        try:
            book = epub.read_epub(epub_path)
            chapters = []
            
            for item in book.get_items():
                if item.get_type() == ebooklib.ITEM_DOCUMENT:
                    # 解析HTML内容
                    soup = BeautifulSoup(item.get_content(), 'html.parser')
                    
                    # 提取文本内容
                    text = soup.get_text()
                    
                    # 清理文本
                    text = re.sub(r'\s+', ' ', text).strip()
                    
                    if len(text) > 100:  # 过滤太短的内容
                        # 尝试从文件名或内容中提取章节标题
                        title = self._extract_chapter_title(item.get_name(), text)
                        
                        chapters.append({
                            'title': title,
                            'content': text,
                            'word_count': len(text)
                        })
                        
            logger.info(f"成功提取 {len(chapters)} 个章节")
            return chapters
            
        except Exception as e:
            logger.error(f"提取EPUB文件失败: {e}")
            raise

    def _extract_chapter_title(self, filename: str, content: str) -> str:
        """
        提取章节标题
        """
        # 从内容开头尝试提取标题
        lines = content.split('\n')[:5]  # 取前5行
        for line in lines:
            line = line.strip()
            if line and (
                '第' in line and '章' in line or
                'Chapter' in line or
                len(line) < 50  # 短行可能是标题
            ):
                return line
        
        # 如果没有找到，使用文件名
        title = os.path.splitext(filename)[0]
        return title if title else "未知章节"

    @retry_on_failure(max_retries=5, delay=3.0, backoff=1.5)
    def analyze_chapter(self, chapter: Dict, chapter_index: int = 0) -> Optional[Dict]:
        """
        分析单个章节的金句
        
        Args:
            chapter: 章节信息字典
            chapter_index: 章节索引
            
        Returns:
            分析结果或None（如果失败）
        """
        try:
            # 检查是否应该继续
            if not self.interrupt_handler.should_continue():
                logger.info("收到中断信号，停止分析")
                return None
                
            logger.info(f"开始分析章节 {chapter_index}: {chapter['title']} (字数: {chapter['word_count']})")
            
            # 如果章节太长，进行分段处理
            content = chapter['content']
            if len(content) > 6000:  # 降低分段阈值，确保API能完整处理
                logger.info(f"章节内容较长，进行分段处理")
                return self._analyze_long_chapter(chapter, chapter_index)
            
            # 构建完整提示词
            full_prompt = self.prompt + content
            
            # 多次尝试不同的API参数组合
            api_configs = [
                {"temperature": 0.3, "max_tokens": self.max_tokens, "timeout": self.api_timeout},
                {"temperature": 0.1, "max_tokens": self.max_tokens, "timeout": self.api_timeout * 1.5},  # 更保守的参数
                {"temperature": 0.5, "max_tokens": self.max_tokens, "timeout": self.api_timeout * 2},   # 更宽松的参数
            ]
            
            last_error = None
            for attempt, config in enumerate(api_configs):
                try:
                    logger.info(f"尝试API配置 {attempt + 1}/{len(api_configs)}")
                    
                    # 调用OpenAI接口
                    response = self.client.chat.completions.create(
                        model=self.model,
                        messages=[
                            {"role": "system", "content": "你是一个专业的文学分析师，擅长识别文学作品中的金句。请确保返回完整的JSON格式响应。"},
                            {"role": "user", "content": full_prompt}
                        ],
                        **config
                    )
                    
                    # 检查响应是否完整
                    if not response.choices or not response.choices[0].message.content:
                        raise ValueError("API返回空响应")
                    
                    result_text = response.choices[0].message.content
                    
                    # 检查响应是否被截断
                    if response.choices[0].finish_reason == "length":
                        logger.warning(f"API响应可能被截断，尝试下一个配置")
                        continue
                    
                    # 解析JSON响应
                    result = self._parse_api_response(result_text, chapter, chapter_index)
                    if result:
                        logger.info(f"章节 {chapter_index} 分析完成: {chapter['title']} - 找到 {len(result.get('golden_sentences', []))} 个金句")
                        
                        # 添加延迟避免API限制
                        time.sleep(self.rate_limit_delay)
                        return result
                    
                except Exception as e:
                    last_error = e
                    logger.warning(f"API配置 {attempt + 1} 失败: {e}")
                    if attempt < len(api_configs) - 1:
                        time.sleep(5)  # 等待后重试
                        continue
                    else:
                        raise last_error
            
            raise last_error or ValueError("所有API配置都失败")
                    
        except openai.RateLimitError as e:
            logger.warning(f"API限制错误: {e}")
            wait_time = 120  # 等待2分钟
            logger.info(f"等待 {wait_time} 秒后重试...")
            time.sleep(wait_time)
            raise
            
        except openai.APITimeoutError as e:
            logger.warning(f"API超时: {e}")
            raise
            
        except Exception as e:
            logger.error(f"分析章节失败: {chapter['title']} - {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            raise

    def _parse_api_response(self, result_text: str, chapter: Dict, chapter_index: int) -> Optional[Dict]:
        """
        智能解析API响应，确保获得完整输出
        """
        try:
            # 尝试直接解析JSON
            result = json.loads(result_text)
            
        except json.JSONDecodeError:
            logger.warning(f"直接JSON解析失败，尝试智能提取")
            
            # 尝试多种方式提取JSON
            extraction_methods = [
                # 方法1: 寻找完整的JSON块
                lambda text: re.search(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', text, re.DOTALL),
                # 方法2: 寻找以{开头的内容
                lambda text: re.search(r'\{.*', text, re.DOTALL),
                # 方法3: 寻找包含关键字段的JSON
                lambda text: re.search(r'\{[^{}]*"chapter_title"[^{}]*\}', text, re.DOTALL),
            ]
            
            result = None
            for method in extraction_methods:
                try:
                    match = method(result_text)
                    if match:
                        json_text = match.group()
                        # 尝试修复常见的JSON问题
                        json_text = self._fix_json_issues(json_text)
                        result = json.loads(json_text)
                        logger.info("使用智能提取成功解析JSON")
                        break
                except:
                    continue
            
            # 如果还是无法解析，尝试从文本中提取关键信息
            if not result:
                logger.warning("JSON解析完全失败，尝试从文本提取信息")
                result = self._extract_from_text(result_text)
        
        if not result:
            logger.error(f"无法从API响应中提取有效信息")
            logger.error(f"原始响应: {result_text}")
            return None
        
        # 确保结果包含必要字段
        result = self._ensure_complete_result(result, chapter, chapter_index)
        
        return result
    
    def _fix_json_issues(self, json_text: str) -> str:
        """修复常见的JSON格式问题"""
        # 移除可能的markdown代码块标记
        json_text = re.sub(r'```json\s*', '', json_text)
        json_text = re.sub(r'```\s*', '', json_text)

        # 移除控制字符
        json_text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', json_text)

        # 尝试修复缺失的逗号
        json_text = re.sub(r'"\s*\n\s*"', '",\n"', json_text)

        # 尝试修复缺失的右括号
        if json_text.count('{') > json_text.count('}'):
            json_text += '}' * (json_text.count('{') - json_text.count('}'))

        return json_text

    def _extract_from_text(self, text: str) -> Dict:
        """从纯文本中提取信息（当JSON解析完全失败时）"""
        result = {
            "chapter_title": "解析失败",
            "golden_sentences": [],
            "chapter_summary": ""
        }

        # 尝试提取金句（寻找引号内的内容）
        sentences = re.findall(r'"([^"]{10,})"', text)
        for sentence in sentences[:5]:  # 最多提取5个金句
            result["golden_sentences"].append({
                "sentence": sentence,
                "speaker": "未知",
                "reason": "从文本提取"
            })

        # 尝试提取摘要（寻找较长的句子）
        lines = text.split('\n')
        summary_lines = [line.strip() for line in lines if len(line.strip()) > 20 and len(line.strip()) < 200]
        if summary_lines:
            result["chapter_summary"] = summary_lines[0]

        logger.info(f"从文本提取了 {len(result['golden_sentences'])} 个金句")
        return result

    def _ensure_complete_result(self, result: Dict, chapter: Dict, chapter_index: int) -> Dict:
        """确保结果包含所有必要字段"""
        # 添加默认值
        if 'chapter_title' not in result:
            result['chapter_title'] = chapter['title']

        if 'golden_sentences' not in result:
            result['golden_sentences'] = []

        if 'chapter_summary' not in result:
            result['chapter_summary'] = ""

        # 确保golden_sentences是列表且格式正确
        if not isinstance(result['golden_sentences'], list):
            result['golden_sentences'] = []

        for sentence_data in result['golden_sentences']:
            if not isinstance(sentence_data, dict):
                continue
            if 'sentence' not in sentence_data:
                sentence_data['sentence'] = ""
            if 'speaker' not in sentence_data:
                sentence_data['speaker'] = "未知"
            if 'reason' not in sentence_data:
                sentence_data['reason'] = "AI分析"

        # 添加元数据
        result['original_title'] = chapter['title']
        result['word_count'] = chapter['word_count']
        result['chapter_index'] = chapter_index
        result['analysis_timestamp'] = datetime.now().isoformat()

        return result

    def _analyze_long_chapter(self, chapter: Dict, chapter_index: int = 0) -> Dict:
        """
        分析长章节（分段处理）
        """
        content = chapter['content']
        segments = []
        
        # 按段落分割，使用更小的分段确保API能完整处理
        paragraphs = content.split('\n')
        current_segment = ""
        
        for paragraph in paragraphs:
            if len(current_segment + paragraph) > 5000:  # 进一步降低分段阈值
                if current_segment.strip():
                    segments.append(current_segment.strip())
                current_segment = paragraph
            else:
                current_segment += "\n" + paragraph
        
        if current_segment.strip():
            segments.append(current_segment.strip())
        
        logger.info(f"长章节分为 {len(segments)} 段处理")
        
        # 分析每段
        all_golden_sentences = []
        chapter_summaries = []
        
        for i, segment in enumerate(segments):
            try:
                # 检查是否应该继续
                if not self.interrupt_handler.should_continue():
                    logger.info("收到中断信号，停止分段分析")
                    break
                    
                logger.info(f"分析章节 {chapter_index} 第 {i+1}/{len(segments)} 段")
                
                segment_chapter = {
                    'title': f"{chapter['title']} - 第{i+1}段",
                    'content': segment,
                    'word_count': len(segment)
                }
                
                # 直接分析这个短段落
                result = self._analyze_segment_directly(segment_chapter, f"{chapter_index}-{i}")
                if result:
                    all_golden_sentences.extend(result.get('golden_sentences', []))
                    if result.get('chapter_summary'):
                        chapter_summaries.append(result['chapter_summary'])
                
                # 添加延迟避免API限制
                time.sleep(self.rate_limit_delay)
                
            except Exception as e:
                logger.error(f"分析章节 {chapter_index} 第 {i+1} 段失败: {e}")
                # 即使单段失败也继续处理其他段
                continue
        
        return {
            'chapter_title': chapter['title'],
            'golden_sentences': all_golden_sentences,
            'chapter_summary': ' '.join(chapter_summaries),
            'original_title': chapter['title'],
            'word_count': chapter['word_count'],
            'segments_count': len(segments),
            'chapter_index': chapter_index,
            'analysis_timestamp': datetime.now().isoformat()
        }
    
    @retry_on_failure(max_retries=5, delay=3.0, backoff=1.5)
    def _analyze_segment_directly(self, segment_chapter: Dict, segment_id: str) -> Optional[Dict]:
        """
        直接分析文本段落（不进行分段处理）
        """
        try:
            # 构建完整提示词
            full_prompt = self.prompt + segment_chapter['content']
            
            # 多次尝试确保获得完整输出
            api_configs = [
                {"temperature": 0.3, "max_tokens": self.max_tokens, "timeout": self.api_timeout},
                {"temperature": 0.1, "max_tokens": self.max_tokens, "timeout": self.api_timeout * 1.5},
                {"temperature": 0.5, "max_tokens": 4000, "timeout": self.api_timeout * 2},  # 增加token数
            ]
            
            last_error = None
            for attempt, config in enumerate(api_configs):
                try:
                    logger.debug(f"段落 {segment_id} 尝试API配置 {attempt + 1}/{len(api_configs)}")
                    
                    # 调用OpenAI接口
                    response = self.client.chat.completions.create(
                        model=self.model,
                        messages=[
                            {"role": "system", "content": "你是一个专业的文学分析师，擅长识别文学作品中的金句。请务必返回完整的JSON格式响应，不要截断。"},
                            {"role": "user", "content": full_prompt}
                        ],
                        **config
                    )
                    
                    # 检查响应完整性
                    if not response.choices or not response.choices[0].message.content:
                        raise ValueError("API返回空响应")
                    
                    result_text = response.choices[0].message.content
                    
                    # 检查是否被截断
                    if response.choices[0].finish_reason == "length":
                        logger.warning(f"段落 {segment_id} API响应被截断，尝试下一个配置")
                        if attempt < len(api_configs) - 1:
                            continue
                        else:
                            # 最后一次尝试，即使被截断也尝试解析
                            logger.warning(f"段落 {segment_id} 所有配置都被截断，尝试解析部分响应")
                    
                    # 解析响应
                    result = self._parse_api_response(result_text, segment_chapter, segment_id)
                    if result:
                        logger.info(f"段落 {segment_id} 分析完成 - 找到 {len(result.get('golden_sentences', []))} 个金句")
                        return result
                    
                except Exception as e:
                    last_error = e
                    logger.warning(f"段落 {segment_id} API配置 {attempt + 1} 失败: {e}")
                    if attempt < len(api_configs) - 1:
                        time.sleep(5)
                        continue
                    else:
                        raise last_error
            
            raise last_error or ValueError("所有API配置都失败")
                    
        except openai.RateLimitError as e:
            logger.warning(f"段落 {segment_id} API限制错误: {e}")
            wait_time = 120
            logger.info(f"等待 {wait_time} 秒后重试...")
            time.sleep(wait_time)
            raise
            
        except openai.APITimeoutError as e:
            logger.warning(f"段落 {segment_id} API超时: {e}")
            raise
            
        except Exception as e:
            logger.error(f"分析段落 {segment_id} 失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            raise

    def analyze_novel(self, epub_path: str, output_path: str = None, resume: bool = True) -> Dict:
        """
        分析整部小说
        
        Args:
            epub_path: EPUB文件路径
            output_path: 输出文件路径（可选）
            resume: 是否从上次中断处恢复
            
        Returns:
            完整的分析结果
        """
        start_time = datetime.now()
        logger.info(f"开始分析小说: {epub_path}")
        
        try:
            # 提取章节
            chapters = self.extract_chapters(epub_path)
            
            if not chapters:
                logger.error("未找到任何章节")
                return {}
            
            total_words = sum(chapter['word_count'] for chapter in chapters)
            logger.info(f"小说总字数: {total_words:,} 字，共 {len(chapters)} 章")
            
            # 设置进度管理器
            self.progress_manager.progress['total_chapters'] = len(chapters)
            if not resume:
                self.progress_manager.reset_progress()
                
            self.progress_manager.progress['start_time'] = start_time.isoformat()
            self.progress_manager.save_progress()
            
            # 检查是否有现有结果文件
            results = self._load_existing_results(output_path) if output_path and resume else None
            
            if results is None:
                # 创建新的结果结构
                results = {
                    'novel_info': {
                        'file_path': epub_path,
                        'total_chapters': len(chapters),
                        'total_words': total_words,
                        'analysis_start_time': start_time.isoformat(),
                        'analysis_end_time': None,
                        'resume_count': 0
                    },
                    'chapters': []
                }
            else:
                # 恢复分析，更新信息
                results['novel_info']['resume_count'] = results['novel_info'].get('resume_count', 0) + 1
                results['novel_info']['last_resume_time'] = start_time.isoformat()
                logger.info(f"从上次中断处恢复分析，已完成 {len(results['chapters'])} 章")
            
            # 获取进度信息
            progress_info = self.progress_manager.get_progress_info()
            logger.info(f"当前进度: {progress_info['completed']}/{progress_info['total']} 章节完成 ({progress_info['completion_rate']:.1f}%)")
            
            # 分析每个章节
            for i, chapter in enumerate(chapters):
                try:
                    # 检查是否应该继续
                    if not self.interrupt_handler.should_continue():
                        logger.info("收到中断信号，保存当前进度并退出")
                        if output_path:
                            self._save_results(results, output_path)
                        return results
                    
                    # 检查章节是否已完成
                    if self.progress_manager.is_chapter_completed(i):
                        logger.info(f"跳过已完成的章节 {i}: {chapter['title']}")
                        continue
                    
                    # 检查结果中是否已有该章节
                    if self._is_chapter_in_results(results, i):
                        logger.info(f"结果中已存在章节 {i}: {chapter['title']}")
                        self.progress_manager.mark_chapter_completed(i)
                        continue
                    
                    logger.info(f"分析进度: {i+1}/{len(chapters)} - {chapter['title']}")
                    
                    # 分析章节
                    result = self.analyze_chapter(chapter, i)
                    if result:
                        results['chapters'].append(result)
                        self.progress_manager.mark_chapter_completed(i)
                        
                        # 定期保存结果（更频繁）
                        if (i + 1) % 3 == 0 and output_path:  # 每3章保存一次
                            self._save_results(results, output_path)
                            logger.info(f"已保存进度到第 {i+1} 章")
                            
                        # 显示详细进度信息
                        progress_info = self.progress_manager.get_progress_info()
                        current_golden_sentences = sum(len(ch.get('golden_sentences', [])) for ch in results['chapters'])
                        logger.info(f"当前进度: {progress_info['completed']}/{progress_info['total']} ({progress_info['completion_rate']:.1f}%) | 金句总数: {current_golden_sentences}")
                    else:
                        self.progress_manager.mark_chapter_failed(i, "分析失败")
                        logger.error(f"章节 {i} 分析失败: {chapter['title']}")
                        
                        # 询问是否继续
                        if not self._should_continue_after_failure(i, chapter['title']):
                            break
                    
                    # 添加延迟避免API限制
                    time.sleep(self.rate_limit_delay)
                    
                except KeyboardInterrupt:
                    logger.info("收到键盘中断信号")
                    break
                except Exception as e:
                    logger.error(f"处理章节 {i} 时发生未预期错误: {e}")
                    logger.error(f"错误详情: {traceback.format_exc()}")
                    
                    self.progress_manager.mark_chapter_failed(i, str(e))
                    
                    # 询问是否继续
                    if not self._should_continue_after_failure(i, chapter['title']):
                        break
            
            # 完成分析
            end_time = datetime.now()
            results['novel_info']['analysis_end_time'] = end_time.isoformat()
            results['novel_info']['analysis_duration'] = str(end_time - start_time)
            
            # 统计信息
            completed_chapters = len(results['chapters'])
            total_golden_sentences = sum(len(ch.get('golden_sentences', [])) for ch in results['chapters'])
            results['novel_info']['completed_chapters'] = completed_chapters
            results['novel_info']['total_golden_sentences'] = total_golden_sentences
            
            # 获取最终进度信息
            final_progress = self.progress_manager.get_progress_info()
            results['novel_info']['final_progress'] = final_progress
            
            logger.info(f"分析完成! 总用时: {end_time - start_time}")
            logger.info(f"完成章节: {completed_chapters}/{len(chapters)}")
            logger.info(f"共找到 {total_golden_sentences} 个金句")
            
            if final_progress['failed'] > 0:
                logger.warning(f"失败章节: {final_progress['failed']} 个")
            
            # 保存最终结果
            if output_path:
                self._save_results(results, output_path)
            
            return results
            
        except Exception as e:
            logger.error(f"分析小说失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            raise

    def _load_existing_results(self, output_path: str) -> Optional[Dict]:
        """加载现有结果文件"""
        if not output_path or not os.path.exists(output_path):
            return None
        
        try:
            with open(output_path, 'r', encoding='utf-8') as f:
                results = json.load(f)
            logger.info(f"已加载现有结果文件: {output_path}")
            return results
        except Exception as e:
            logger.error(f"加载结果文件失败: {e}")
            return None

    def _is_chapter_in_results(self, results: Dict, chapter_index: int) -> bool:
        """检查章节是否已在结果中"""
        for chapter in results.get('chapters', []):
            if chapter.get('chapter_index') == chapter_index:
                return True
        return False

    def _should_continue_after_failure(self, chapter_index: int, chapter_title: str) -> bool:
        """询问是否在失败后继续"""
        try:
            logger.error(f"章节 {chapter_index} ({chapter_title}) 分析失败")
            
            # 在非交互环境中，自动继续
            if not sys.stdin.isatty():
                logger.info("非交互环境，自动继续下一章节")
                return True
            
            # 显示当前进度和统计信息
            progress_info = self.progress_manager.get_progress_info()
            print(f"\n=== 当前状态 ===")
            print(f"完成: {progress_info['completed']} | 失败: {progress_info['failed']} | 剩余: {progress_info['remaining']}")
            print(f"完成率: {progress_info['completion_rate']:.1f}%")
            
            response = input("是否继续分析下一章节？(y/n/q - 继续/跳过/退出): ").lower().strip()
            
            if response == 'q' or response == 'quit':
                logger.info("用户选择退出")
                return False
            elif response == 'n' or response == 'no':
                logger.info("用户选择跳过")
                return False
            else:
                logger.info("继续分析下一章节")
                return True
                
        except KeyboardInterrupt:
            logger.info("收到键盘中断信号")
            return False
        except:
            # 如果输入有问题，默认继续
            return True

    def set_api_parameters(self, max_tokens: int = None, timeout: float = None, rate_limit_delay: float = None, model: str = None):
        """动态调整API参数"""
        if max_tokens is not None:
            self.max_tokens = max_tokens
            logger.info(f"更新max_tokens为: {max_tokens}")

        if timeout is not None:
            self.api_timeout = timeout
            logger.info(f"更新API超时为: {timeout}秒")

        if rate_limit_delay is not None:
            self.rate_limit_delay = rate_limit_delay
            logger.info(f"更新API调用间隔为: {rate_limit_delay}秒")

        if model is not None:
            self.model = model
            logger.info(f"更新AI模型为: {model}")

    def get_api_usage_stats(self) -> Dict:
        """获取API使用统计"""
        progress_info = self.progress_manager.get_progress_info()
        
        # 估算API调用次数和费用
        completed_chapters = progress_info['completed']
        failed_chapters = progress_info['failed']
        
        # 假设每章平均调用1-3次API（包括重试）
        estimated_api_calls = completed_chapters * 2 + failed_chapters * 3
        
        # 估算token使用（粗略估计）
        estimated_input_tokens = completed_chapters * 2000  # 平均每章2000 tokens输入
        estimated_output_tokens = completed_chapters * 800  # 平均每章800 tokens输出
        
        return {
            'completed_chapters': completed_chapters,
            'failed_chapters': failed_chapters,
            'estimated_api_calls': estimated_api_calls,
            'estimated_input_tokens': estimated_input_tokens,
            'estimated_output_tokens': estimated_output_tokens,
            'estimated_total_tokens': estimated_input_tokens + estimated_output_tokens
        }

    def retry_failed_chapters(self, output_path: str) -> Dict:
        """重试失败的章节"""
        logger.info("开始重试失败的章节...")
        
        # 加载现有结果
        results = self._load_existing_results(output_path)
        if not results:
            logger.error("未找到现有结果文件")
            return {}
        
        # 获取失败的章节列表
        failed_chapters = self.progress_manager.progress.get('failed_chapters', [])
        if not failed_chapters:
            logger.info("没有失败的章节需要重试")
            return results
        
        logger.info(f"找到 {len(failed_chapters)} 个失败的章节")
        
        # 重新加载章节
        epub_path = results['novel_info']['file_path']
        chapters = self.extract_chapters(epub_path)
        
        retry_count = 0
        success_count = 0
        
        for failed_info in failed_chapters:
            try:
                chapter_index = failed_info['index']
                
                # 检查是否应该继续
                if not self.interrupt_handler.should_continue():
                    logger.info("收到中断信号，停止重试")
                    break
                
                if chapter_index >= len(chapters):
                    logger.warning(f"章节索引 {chapter_index} 超出范围")
                    continue
                
                chapter = chapters[chapter_index]
                logger.info(f"重试章节 {chapter_index}: {chapter['title']}")
                
                retry_count += 1
                result = self.analyze_chapter(chapter, chapter_index)
                
                if result:
                    # 更新结果
                    self._update_chapter_in_results(results, result)
                    self.progress_manager.mark_chapter_completed(chapter_index)
                    success_count += 1
                    logger.info(f"章节 {chapter_index} 重试成功")
                else:
                    logger.error(f"章节 {chapter_index} 重试仍然失败")
                
                # 添加延迟
                time.sleep(self.rate_limit_delay)
                
            except Exception as e:
                logger.error(f"重试章节 {chapter_index} 时发生错误: {e}")
                continue
        
        logger.info(f"重试完成: {success_count}/{retry_count} 章节成功")
        
        # 保存更新后的结果
        if output_path:
            self._save_results(results, output_path)
        
        return results

    def _update_chapter_in_results(self, results: Dict, new_chapter: Dict):
        """更新结果中的章节"""
        chapter_index = new_chapter.get('chapter_index')
        
        # 查找是否已存在该章节
        for i, chapter in enumerate(results.get('chapters', [])):
            if chapter.get('chapter_index') == chapter_index:
                results['chapters'][i] = new_chapter
                return
        
        # 如果不存在，添加新章节
        results['chapters'].append(new_chapter)

    def get_analysis_status(self) -> Dict:
        """获取分析状态"""
        progress_info = self.progress_manager.get_progress_info()
        
        status = {
            'progress': progress_info,
            'interrupted': self.interrupt_handler.interrupted,
            'failed_chapters': self.progress_manager.progress.get('failed_chapters', []),
            'last_save_time': self.progress_manager.progress.get('last_save_time'),
            'start_time': self.progress_manager.progress.get('start_time')
        }
        
        return status

    def clean_progress(self):
        """清理进度文件"""
        self.progress_manager.reset_progress()
        logger.info("进度文件已清理")

    def _save_results(self, results: Dict, output_path: str):
        """保存分析结果"""
        try:
            # 确保目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"分析结果已保存到: {output_path}")
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            raise

    def generate_report(self, results: Dict, report_path: str):
        """
        生成分析报告
        
        Args:
            results: 分析结果
            report_path: 报告保存路径
        """
        logger.info("生成分析报告...")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 小说金句分析报告\n\n")
            
            # 基本信息
            info = results['novel_info']
            f.write(f"## 基本信息\n")
            f.write(f"- 文件路径: {info['file_path']}\n")
            f.write(f"- 总章节数: {info['total_chapters']}\n")
            f.write(f"- 总字数: {info['total_words']:,}\n")
            f.write(f"- 总金句数: {info['total_golden_sentences']}\n")
            f.write(f"- 分析用时: {info.get('analysis_duration', 'N/A')}\n\n")
            
            # 章节详情
            for chapter in results['chapters']:
                f.write(f"## {chapter['chapter_title']}\n")
                f.write(f"字数: {chapter['word_count']}\n")
                f.write(f"金句数: {len(chapter.get('golden_sentences', []))}\n\n")
                
                if chapter.get('chapter_summary'):
                    f.write(f"**章节总结**: {chapter['chapter_summary']}\n\n")
                
                if chapter.get('golden_sentences'):
                    f.write("**金句列表**:\n")
                    for i, sentence in enumerate(chapter['golden_sentences'], 1):
                        f.write(f"{i}. **{sentence['speaker']}**: {sentence['sentence']}\n")
                        f.write(f"   *理由*: {sentence['reason']}\n\n")
                
                f.write("---\n\n")
        
        logger.info(f"报告已生成: {report_path}")


# 使用示例和命令行接口
def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='EPUB小说金句分析器')
    parser.add_argument('epub_path', help='EPUB文件路径')
    parser.add_argument('--api-key', required=True, help='OpenAI API密钥')
    parser.add_argument('--base-url', default='https://api.openai.com/v1', help='API基础URL')
    parser.add_argument('--output', default='analysis_results.json', help='输出文件路径')
    parser.add_argument('--report', default='analysis_report.md', help='报告文件路径')
    parser.add_argument('--no-resume', action='store_true', help='不从上次中断处恢复')
    parser.add_argument('--retry-failed', action='store_true', help='仅重试失败的章节')
    parser.add_argument('--clean-progress', action='store_true', help='清理进度文件')
    parser.add_argument('--max-tokens', type=int, default=3000, help='API最大token数')
    parser.add_argument('--timeout', type=float, default=180.0, help='API超时时间（秒）')
    parser.add_argument('--delay', type=float, default=2.0, help='API调用间隔（秒）')
    parser.add_argument('--model', default='gpt-3.5-turbo', help='使用的AI模型')
    parser.add_argument('--stats', action='store_true', help='显示API使用统计')
    parser.add_argument('--test-mode', action='store_true', help='测试模式（仅分析前3章）')
    
    args = parser.parse_args()
    
    try:
        # 创建分析器
        analyzer = EPUBAnalyzer(args.api_key, args.base_url)
        
        # 设置API参数
        analyzer.set_api_parameters(
            max_tokens=args.max_tokens,
            timeout=args.timeout,
            rate_limit_delay=args.delay,
            model=args.model
        )
        
        if args.clean_progress:
            analyzer.clean_progress()
            print("进度文件已清理")
            return
        
        if args.stats:
            stats = analyzer.get_api_usage_stats()
            print("=== API使用统计 ===")
            print(f"完成章节: {stats['completed_chapters']}")
            print(f"失败章节: {stats['failed_chapters']}")
            print(f"估算API调用次数: {stats['estimated_api_calls']}")
            print(f"估算输入tokens: {stats['estimated_input_tokens']:,}")
            print(f"估算输出tokens: {stats['estimated_output_tokens']:,}")
            print(f"估算总tokens: {stats['estimated_total_tokens']:,}")
            return
        
        if args.status:
            status = analyzer.get_analysis_status()
            print("=== 分析状态 ===")
            print(f"完成章节: {status['progress']['completed']}")
            print(f"失败章节: {status['progress']['failed']}")
            print(f"剩余章节: {status['progress']['remaining']}")
            print(f"总章节数: {status['progress']['total']}")
            print(f"完成率: {status['progress']['completion_rate']:.1f}%")
            print(f"是否中断: {status['interrupted']}")
            if status['failed_chapters']:
                print(f"失败章节详情: {len(status['failed_chapters'])} 个")
                for failed in status['failed_chapters'][-3:]:  # 显示最近3个失败
                    print(f"  - 章节 {failed['index']}: {failed['error']}")
            return
        
        if args.test_mode:
            logger.info("测试模式：仅分析前3章")
            
        if args.retry_failed:
            # 重试失败的章节
            results = analyzer.retry_failed_chapters(args.output)
            print("失败章节重试完成")
        else:
            # 正常分析
            resume = not args.no_resume
            results = analyzer.analyze_novel(args.epub_path, args.output, resume)
            
            # 生成报告
            if results and results.get('chapters'):
                analyzer.generate_report(results, args.report)
                print(f"分析报告已生成: {args.report}")
        
        if results:
            print("分析完成！生成的文件：")
            print(f"- 详细结果: {args.output}")
            if not args.retry_failed and not args.status and not args.stats:
                print(f"- 分析报告: {args.report}")
            print(f"- 日志文件: novel_analysis.log")
            print(f"- 进度文件: analysis_progress.pkl")
            
            # 显示统计信息
            info = results.get('novel_info', {})
            if info:
                print(f"\n=== 分析统计 ===")
                print(f"总章节数: {info.get('total_chapters', 0)}")
                print(f"完成章节: {info.get('completed_chapters', 0)}")
                print(f"总字数: {info.get('total_words', 0):,}")
                print(f"总金句数: {info.get('total_golden_sentences', 0)}")
                if info.get('analysis_duration'):
                    print(f"分析用时: {info['analysis_duration']}")
            
            # 显示API使用统计
            stats = analyzer.get_api_usage_stats()
            print(f"\n=== API使用情况 ===")
            print(f"估算API调用: {stats['estimated_api_calls']} 次")
            print(f"估算总tokens: {stats['estimated_total_tokens']:,}")
            
            # 给出优化建议
            if stats['failed_chapters'] > stats['completed_chapters'] * 0.1:  # 失败率超过10%
                print(f"\n⚠️  失败率较高({stats['failed_chapters']}/{stats['completed_chapters'] + stats['failed_chapters']})，建议：")
                print("- 增加API超时时间: --timeout 300")
                print("- 增加调用间隔: --delay 3.0")
                print("- 使用更强的模型: --model gpt-4")
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        print("进度已保存，可使用相同命令恢复分析")
        print("或使用 --status 查看当前状态")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"程序执行失败，请查看日志文件: novel_analysis.log")
        print(f"错误信息: {e}")
        print("\n可能的解决方案：")
        print("1. 检查API密钥和网络连接")
        print("2. 增加超时时间: --timeout 300")
        print("3. 使用测试模式验证: --test-mode")
        print("4. 查看详细状态: --status")


if __name__ == "__main__":
    main()




    def analyze_novel(self, epub_path: str, output_path: str = None, resume: bool = True) -> Dict:
        """
        分析整部小说
        
        Args:
            epub_path: EPUB文件路径
            output_path: 输出文件路径（可选）
            resume: 是否从上次中断处恢复
            
        Returns:
            完整的分析结果
        """
        start_time = datetime.now()
        logger.info(f"开始分析小说: {epub_path}")
        
        try:
            # 提取章节
            chapters = self.extract_chapters(epub_path)
            
            if not chapters:
                logger.error("未找到任何章节")
                return {}
            
            total_words = sum(chapter['word_count'] for chapter in chapters)
            logger.info(f"小说总字数: {total_words:,} 字，共 {len(chapters)} 章")
            
            # 设置进度管理器
            self.progress_manager.progress['total_chapters'] = len(chapters)
            if not resume:
                self.progress_manager.reset_progress()
                
            self.progress_manager.progress['start_time'] = start_time.isoformat()
            self.progress_manager.save_progress()
            
            # 检查是否有现有结果文件
            results = self._load_existing_results(output_path) if output_path and resume else None
            
            if results is None:
                # 创建新的结果结构
                results = {
                    'novel_info': {
                        'file_path': epub_path,
                        'total_chapters': len(chapters),
                        'total_words': total_words,
                        'analysis_start_time': start_time.isoformat(),
                        'analysis_end_time': None,
                        'resume_count': 0
                    },
                    'chapters': []
                }
            else:
                # 恢复分析，更新信息
                results['novel_info']['resume_count'] = results['novel_info'].get('resume_count', 0) + 1
                results['novel_info']['last_resume_time'] = start_time.isoformat()
                logger.info(f"从上次中断处恢复分析，已完成 {len(results['chapters'])} 章")
            
            # 获取进度信息
            progress_info = self.progress_manager.get_progress_info()
            logger.info(f"当前进度: {progress_info['completed']}/{progress_info['total']} 章节完成 ({progress_info['completion_rate']:.1f}%)")
            
            # 分析每个章节
            for i, chapter in enumerate(chapters):
                try:
                    # 检查是否应该继续
                    if not self.interrupt_handler.should_continue():
                        logger.info("收到中断信号，保存当前进度并退出")
                        if output_path:
                            self._save_results(results, output_path)
                        return results
                    
                    # 检查章节是否已完成
                    if self.progress_manager.is_chapter_completed(i):
                        logger.info(f"跳过已完成的章节 {i}: {chapter['title']}")
                        continue
                    
                    # 检查结果中是否已有该章节
                    if self._is_chapter_in_results(results, i):
                        logger.info(f"结果中已存在章节 {i}: {chapter['title']}")
                        self.progress_manager.mark_chapter_completed(i)
                        continue
                    
                    logger.info(f"分析进度: {i+1}/{len(chapters)} - {chapter['title']}")
                    
                    # 分析章节
                    result = self.analyze_chapter(chapter, i)
                    if result:
                        results['chapters'].append(result)
                        self.progress_manager.mark_chapter_completed(i)
                        
                        # 定期保存结果
                        if (i + 1) % 5 == 0 and output_path:
                            self._save_results(results, output_path)
                            logger.info(f"已保存进度到第 {i+1} 章")
                    else:
                        self.progress_manager.mark_chapter_failed(i, "分析失败")
                        logger.error(f"章节 {i} 分析失败: {chapter['title']}")
                        
                        # 询问是否继续
                        if not self._should_continue_after_failure(i, chapter['title']):
                            break
                    
                    # 添加延迟避免API限制
                    time.sleep(self.rate_limit_delay)
                    
                except KeyboardInterrupt:
                    logger.info("收到键盘中断信号")
                    break
                except Exception as e:
                    logger.error(f"处理章节 {i} 时发生未预期错误: {e}")
                    logger.error(f"错误详情: {traceback.format_exc()}")
                    
                    self.progress_manager.mark_chapter_failed(i, str(e))
                    
                    # 询问是否继续
                    if not self._should_continue_after_failure(i, chapter['title']):
                        break
            
            # 完成分析
            end_time = datetime.now()
            results['novel_info']['analysis_end_time'] = end_time.isoformat()
            results['novel_info']['analysis_duration'] = str(end_time - start_time)
            
            # 统计信息
            completed_chapters = len(results['chapters'])
            total_golden_sentences = sum(len(ch.get('golden_sentences', [])) for ch in results['chapters'])
            results['novel_info']['completed_chapters'] = completed_chapters
            results['novel_info']['total_golden_sentences'] = total_golden_sentences
            
            # 获取最终进度信息
            final_progress = self.progress_manager.get_progress_info()
            results['novel_info']['final_progress'] = final_progress
            
            logger.info(f"分析完成! 总用时: {end_time - start_time}")
            logger.info(f"完成章节: {completed_chapters}/{len(chapters)}")
            logger.info(f"共找到 {total_golden_sentences} 个金句")
            
            if final_progress['failed'] > 0:
                logger.warning(f"失败章节: {final_progress['failed']} 个")
            
            # 保存最终结果
            if output_path:
                self._save_results(results, output_path)
            
            return results
            
        except Exception as e:
            logger.error(f"分析小说失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            raise

    def _load_existing_results(self, output_path: str) -> Optional[Dict]:
        """加载现有结果文件"""
        if not output_path or not os.path.exists(output_path):
            return None

        try:
            with open(output_path, 'r', encoding='utf-8') as f:
                results = json.load(f)
            logger.info(f"已加载现有结果文件: {output_path}")
            return results
        except Exception as e:
            logger.error(f"加载结果文件失败: {e}")
            return None

    def _is_chapter_in_results(self, results: Dict, chapter_index: int) -> bool:
        """检查章节是否已在结果中"""
        for chapter in results.get('chapters', []):
            if chapter.get('chapter_index') == chapter_index:
                return True
        return False

    def _should_continue_after_failure(self, chapter_index: int, chapter_title: str) -> bool:
        """询问是否在失败后继续"""
        try:
            logger.error(f"章节 {chapter_index} ({chapter_title}) 分析失败")
            
            # 在非交互环境中，自动继续
            if not sys.stdin.isatty():
                logger.info("非交互环境，自动继续下一章节")
                return True
            
            response = input("是否继续分析下一章节？(y/n/q - 继续/跳过/退出): ").lower().strip()
            
            if response == 'q':
                logger.info("用户选择退出")
                return False
            elif response == 'n':
                logger.info("用户选择跳过")
                return False
            else:
                logger.info("继续分析下一章节")
                return True
                
        except KeyboardInterrupt:
            logger.info("收到键盘中断信号")
            return False
        except:
            # 如果输入有问题，默认继续
            return True

    def retry_failed_chapters(self, output_path: str) -> Dict:
        """重试失败的章节"""
        logger.info("开始重试失败的章节...")
        
        # 加载现有结果
        results = self._load_existing_results(output_path)
        if not results:
            logger.error("未找到现有结果文件")
            return {}
        
        # 获取失败的章节列表
        failed_chapters = self.progress_manager.progress.get('failed_chapters', [])
        if not failed_chapters:
            logger.info("没有失败的章节需要重试")
            return results
        
        logger.info(f"找到 {len(failed_chapters)} 个失败的章节")
        
        # 重新加载章节
        epub_path = results['novel_info']['file_path']
        chapters = self.extract_chapters(epub_path)
        
        retry_count = 0
        success_count = 0
        
        for failed_info in failed_chapters:
            try:
                chapter_index = failed_info['index']
                
                # 检查是否应该继续
                if not self.interrupt_handler.should_continue():
                    logger.info("收到中断信号，停止重试")
                    break
                
                if chapter_index >= len(chapters):
                    logger.warning(f"章节索引 {chapter_index} 超出范围")
                    continue
                
                chapter = chapters[chapter_index]
                logger.info(f"重试章节 {chapter_index}: {chapter['title']}")
                
                retry_count += 1
                result = self.analyze_chapter(chapter, chapter_index)
                
                if result:
                    # 更新结果
                    self._update_chapter_in_results(results, result)
                    self.progress_manager.mark_chapter_completed(chapter_index)
                    success_count += 1
                    logger.info(f"章节 {chapter_index} 重试成功")
                else:
                    logger.error(f"章节 {chapter_index} 重试仍然失败")
                
                # 添加延迟
                time.sleep(self.rate_limit_delay)
                
            except Exception as e:
                logger.error(f"重试章节 {chapter_index} 时发生错误: {e}")
                continue
        
        logger.info(f"重试完成: {success_count}/{retry_count} 章节成功")
        
        # 保存更新后的结果
        if output_path:
            self._save_results(results, output_path)
        
        return results

    def _update_chapter_in_results(self, results: Dict, new_chapter: Dict):
        """更新结果中的章节"""
        chapter_index = new_chapter.get('chapter_index')
        
        # 查找是否已存在该章节
        for i, chapter in enumerate(results.get('chapters', [])):
            if chapter.get('chapter_index') == chapter_index:
                results['chapters'][i] = new_chapter
                return
        
        # 如果不存在，添加新章节
        results['chapters'].append(new_chapter)

    def get_analysis_status(self) -> Dict:
        """获取分析状态"""
        progress_info = self.progress_manager.get_progress_info()
        
        status = {
            'progress': progress_info,
            'interrupted': self.interrupt_handler.interrupted,
            'failed_chapters': self.progress_manager.progress.get('failed_chapters', []),
            'last_save_time': self.progress_manager.progress.get('last_save_time'),
            'start_time': self.progress_manager.progress.get('start_time')
        }
        
        return status

    def clean_progress(self):
        """清理进度文件"""
        self.progress_manager.reset_progress()
        logger.info("进度文件已清理")

    def _save_results(self, results: Dict, output_path: str):
        """保存分析结果"""
        try:
            # 确保目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"分析结果已保存到: {output_path}")
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            raise

    def generate_report(self, results: Dict, report_path: str):
        """
        生成分析报告
        
        Args:
            results: 分析结果
            report_path: 报告保存路径
        """
        logger.info("生成分析报告...")
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 小说金句分析报告\n\n")
            
            # 基本信息
            info = results['novel_info']
            f.write(f"## 基本信息\n")
            f.write(f"- 文件路径: {info['file_path']}\n")
            f.write(f"- 总章节数: {info['total_chapters']}\n")
            f.write(f"- 总字数: {info['total_words']:,}\n")
            f.write(f"- 总金句数: {info['total_golden_sentences']}\n")
            f.write(f"- 分析用时: {info.get('analysis_duration', 'N/A')}\n\n")
            
            # 章节详情
            for chapter in results['chapters']:
                f.write(f"## {chapter['chapter_title']}\n")
                f.write(f"字数: {chapter['word_count']}\n")
                f.write(f"金句数: {len(chapter.get('golden_sentences', []))}\n\n")
                
                if chapter.get('chapter_summary'):
                    f.write(f"**章节总结**: {chapter['chapter_summary']}\n\n")
                
                if chapter.get('golden_sentences'):
                    f.write("**金句列表**:\n")
                    for i, sentence in enumerate(chapter['golden_sentences'], 1):
                        f.write(f"{i}. **{sentence['speaker']}**: {sentence['sentence']}\n")
                        f.write(f"   *理由*: {sentence['reason']}\n\n")
                
                f.write("---\n\n")
        
        logger.info(f"报告已生成: {report_path}")


# 使用示例和命令行接口
def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='EPUB小说金句分析器')
    parser.add_argument('epub_path', help='EPUB文件路径')
    parser.add_argument('--api-key', required=True, help='OpenAI API密钥')
    parser.add_argument('--base-url', default='https://api.openai.com/v1', help='API基础URL')
    parser.add_argument('--output', default='analysis_results.json', help='输出文件路径')
    parser.add_argument('--report', default='analysis_report.md', help='报告文件路径')
    parser.add_argument('--no-resume', action='store_true', help='不从上次中断处恢复')
    parser.add_argument('--retry-failed', action='store_true', help='仅重试失败的章节')
    parser.add_argument('--clean-progress', action='store_true', help='清理进度文件')
    parser.add_argument('--status', action='store_true', help='显示分析状态')
    
    args = parser.parse_args()
    
    try:
        # 创建分析器
        analyzer = EPUBAnalyzer(args.api_key, args.base_url)
        
        if args.clean_progress:
            analyzer.clean_progress()
            print("进度文件已清理")
            return
        
        if args.status:
            status = analyzer.get_analysis_status()
            print("=== 分析状态 ===")
            print(f"完成章节: {status['progress']['completed']}")
            print(f"失败章节: {status['progress']['failed']}")
            print(f"剩余章节: {status['progress']['remaining']}")
            print(f"总章节数: {status['progress']['total']}")
            print(f"完成率: {status['progress']['completion_rate']:.1f}%")
            print(f"是否中断: {status['interrupted']}")
            if status['failed_chapters']:
                print(f"失败章节详情: {len(status['failed_chapters'])} 个")
            return
        
        if args.retry_failed:
            # 重试失败的章节
            results = analyzer.retry_failed_chapters(args.output)
            print("失败章节重试完成")
        else:
            # 正常分析
            resume = not args.no_resume
            results = analyzer.analyze_novel(args.epub_path, args.output, resume)
            
            # 生成报告
            if results and results.get('chapters'):
                analyzer.generate_report(results, args.report)
                print(f"分析报告已生成: {args.report}")
        
        if results:
            print("分析完成！生成的文件：")
            print(f"- 详细结果: {args.output}")
            if not args.retry_failed and not args.status:
                print(f"- 分析报告: {args.report}")
            print(f"- 日志文件: novel_analysis.log")
            print(f"- 进度文件: analysis_progress.pkl")
            
            # 显示统计信息
            info = results.get('novel_info', {})
            if info:
                print(f"\n=== 分析统计 ===")
                print(f"总章节数: {info.get('total_chapters', 0)}")
                print(f"完成章节: {info.get('completed_chapters', 0)}")
                print(f"总字数: {info.get('total_words', 0):,}")
                print(f"总金句数: {info.get('total_golden_sentences', 0)}")
                if info.get('analysis_duration'):
                    print(f"分析用时: {info['analysis_duration']}")
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        print("进度已保存，可使用相同命令恢复分析")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"程序执行失败，请查看日志文件: novel_analysis.log")
        print(f"错误信息: {e}")


if __name__ == "__main__":
    main()
